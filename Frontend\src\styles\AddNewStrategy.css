/* AddNewStrategy Component Styles */
.AddNewStrategy {
  padding: var(--p-20);
  max-width: 100%;
  background-color: var(--white);
}

/* Header Section */
.AddNewStrategy__header {
  display: flex;
  align-items: center;
  gap: var(--gap-20);
  margin-bottom: var(--mb-30);
  padding-bottom: var(--pb-20);
  border-bottom: 1px solid var(--light-gray);
}

.AddNewStrategy__back-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border: none;
  background-color: var(--bg-gray);
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: all 0.3s ease;
  color: var(--text-color);
  font-size: var(--heading5);
}

.AddNewStrategy__back-btn:hover {
  background-color: var(--primary-color);
  color: var(--white);
  transform: translateX(-2px);
}

.AddNewStrategy__title {
  font-size: var(--heading4);
  font-weight: 600;
  color: var(--secondary-color);
  margin: 0;
}

/* Form Styles */
.AddNewStrategy__form {
  display: flex;
  flex-direction: column;
  gap: var(--gap-30);
}

/* Basic Info Grid */
.AddNewStrategy__basic-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--gap-20);
}

/* Field Styles */
.AddNewStrategy__field {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.AddNewStrategy__label {
  font-size: var(--basefont);
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: 5px;
}

.AddNewStrategy__input,
.AddNewStrategy__select {
  padding: var(--p-15);
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
  font-size: var(--basefont);
  color: var(--text-color);
  background-color: var(--white);
  transition: border-color 0.3s ease;
}

.AddNewStrategy__input:focus,
.AddNewStrategy__select:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(var(--primary-rgb), 0.1);
}

.AddNewStrategy__input::placeholder {
  color: var(--dark-gray);
}

/* Rich Text Section */
.AddNewStrategy__rich-text-section {
  display: flex;
  flex-direction: column;
  gap: var(--gap-30);
}

.AddNewStrategy__quill {
  background-color: var(--white);
  border-radius: var(--border-radius);
}

.AddNewStrategy__quill .ql-container {
  border-bottom-left-radius: var(--border-radius);
  border-bottom-right-radius: var(--border-radius);
  min-height: 120px;
}

.AddNewStrategy__quill .ql-toolbar {
  border-top-left-radius: var(--border-radius);
  border-top-right-radius: var(--border-radius);
  border-color: var(--light-gray);
}

.AddNewStrategy__quill .ql-container {
  border-color: var(--light-gray);
}

.AddNewStrategy__quill .ql-editor {
  font-size: var(--basefont);
  line-height: 1.6;
}

.AddNewStrategy__quill .ql-editor.ql-blank::before {
  color: var(--dark-gray);
  font-style: normal;
}

/* Upload Section */
.AddNewStrategy__upload-section {
  display: flex;
  flex-direction: column;
  gap: var(--gap-10);
}

.AddNewStrategy__upload-box {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--p-40);
  border: 2px dashed var(--light-gray);
  border-radius: var(--border-radius-large);
  background-color: var(--bg-gray);
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
}

.AddNewStrategy__upload-box:hover {
  border-color: var(--primary-color);
  background-color: rgba(var(--primary-rgb), 0.05);
}

.AddNewStrategy__upload-icon {
  font-size: 48px;
  color: var(--dark-gray);
  margin-bottom: var(--mb-15);
}

.AddNewStrategy__upload-text {
  font-size: var(--basefont);
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: var(--mb-10);
}

.AddNewStrategy__upload-subtext {
  font-size: var(--smallfont);
  color: var(--dark-gray);
  margin: 0;
}

/* Form Actions */
.AddNewStrategy__actions {
  display: flex;
  gap: var(--gap-20);
  justify-content: flex-start;
  padding-top: var(--pt-20);
  border-top: 1px solid var(--light-gray);
}

.AddNewStrategy__submit-btn {
  min-width: 180px;
}

.AddNewStrategy__clear-btn {
  min-width: 120px;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .AddNewStrategy__basic-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .AddNewStrategy {
    padding: var(--p-15);
  }

  .AddNewStrategy__header {
    gap: var(--gap-15);
    margin-bottom: var(--mb-20);
  }

  .AddNewStrategy__title {
    font-size: var(--heading5);
  }

  .AddNewStrategy__basic-grid {
    grid-template-columns: 1fr;
    gap: var(--gap-15);
  }

  .AddNewStrategy__form {
    gap: var(--gap-20);
  }

  .AddNewStrategy__rich-text-section {
    gap: var(--gap-20);
  }

  .AddNewStrategy__upload-box {
    padding: var(--p-30);
  }

  .AddNewStrategy__upload-icon {
    font-size: 36px;
  }

  .AddNewStrategy__actions {
    flex-direction: column;
    gap: var(--gap-15);
  }

  .AddNewStrategy__submit-btn,
  .AddNewStrategy__clear-btn {
    width: 100%;
    min-width: auto;
  }
}

@media (max-width: 480px) {
  .AddNewStrategy {
    padding: var(--p-10);
  }

  .AddNewStrategy__header {
    gap: var(--gap-10);
    margin-bottom: var(--mb-15);
  }

  .AddNewStrategy__back-btn {
    width: 36px;
    height: 36px;
    font-size: var(--basefont);
  }

  .AddNewStrategy__title {
    font-size: var(--heading6);
  }

  .AddNewStrategy__input,
  .AddNewStrategy__select {
    padding: var(--p-10);
    font-size: var(--smallfont);
  }

  .AddNewStrategy__upload-box {
    padding: var(--p-20);
  }

  .AddNewStrategy__upload-icon {
    font-size: 28px;
  }

  .AddNewStrategy__upload-text {
    font-size: var(--smallfont);
  }

  .AddNewStrategy__upload-subtext {
    font-size: var(--extrasmallfont);
  }
}
