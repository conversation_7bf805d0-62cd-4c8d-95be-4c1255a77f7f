import React, { lazy, Suspense, useState, useEffect } from "react";
import { Routes, Route } from "react-router-dom";
import { ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import Footer from "../src/components/common/Footer";

// Common Components
import Navbar from "./components/common/Navbar";
import ScrollToTop from "./components/common/ScrollToTop";
import Preloader from "./components/common/Preloader";
import ProtectedRoute from "./components/common/ProtectedRoute";

// Lenis Smooth Scrolling Provider
import LenisProvider from "./utils/LenisProvider";

// Import and preload Lottie animation data
import preloaderAnimation from "./assets/preloader-animation.json";

// Preload the animation data
const preloadedAnimation = { ...preloaderAnimation };

// Lazy-loaded Authentication
const Auth = lazy(() => import("./pages/Authentication/Auth"));
const Signup = lazy(() => import("./pages/Authentication/Signup"));
const OtpVerification = lazy(() =>
  import("./pages/Authentication/OtpVerification")
);

// Lazy-loaded Visitor Pages
const Home = lazy(() => import("./pages/Visitor/Home"));
const Contact = lazy(() => import("./pages/Visitor/Contact"));
const Info = lazy(() => import("./pages/Visitor/Info"));
const ThankYou = lazy(() => import("./pages/Visitor/ThankYou"));

// Lazy-loaded Buyer Pages
const BuyerDashboard = lazy(() => import("./pages/Buyer/BuyerDashboard"));
const BuyerOrders = lazy(() => import("./pages/Buyer/BuyerOrders"));
const BuyerSettings = lazy(() => import("./pages/Buyer/BuyerSettings"));
const BuyerAccount = lazy(() => import("./pages/Buyer/BuyerAccount"));
const BuyerAccountDashboard = lazy(() =>
  import("./pages/Buyer/BuyerAccountDashboard")
);
const ItemDetails = lazy(() => import("./pages/Buyer/ItemDetails"));

// Lazy-loaded Seller Pages
const SellerDashboard = lazy(() => import("./pages/Seller/SellerDashboard"));
const SellerMyContent = lazy(() => import("./pages/Seller/SellerMyContent"));
const SellerSettings = lazy(() => import("./pages/Seller/SellerSettings"));
const SellerMySportsStrategies = lazy(() =>
  import("./pages/Seller/SellerMySportsStrategies")
);
const AddNewStrategy = lazy(() => import("./pages/Seller/AddNewStrategy"));
const SellerRequests = lazy(() => import("./pages/Seller/SellerRequests"));
const SellerBids = lazy(() => import("./pages/Seller/SellerBids"));
const SellerCards = lazy(() => import("./pages/Seller/SellerCards"));
const SellerProfile = lazy(() => import("./pages/Seller/SellerProfile"));

const App = () => {
  const [isLoading, setIsLoading] = useState(true);
  const [contentLoaded, setContentLoaded] = useState(false);
  const [animationLoaded, setAnimationLoaded] = useState(false);

  // Check if both animation and content are loaded
  const checkAllLoaded = () => {
    if (animationLoaded && contentLoaded) {
      // Add a small delay for a smooth transition
      setTimeout(() => {
        setIsLoading(false);
      }, 300);
    }
  };

  // Handle animation loaded callback
  const handleAnimationLoaded = () => {
    setAnimationLoaded(true);
    checkAllLoaded();
  };

  // Handle content loaded
  useEffect(() => {
    // Listen for when the page content is fully loaded
    const handleLoad = () => {
      setContentLoaded(true);
      checkAllLoaded();
    };

    // Check if already loaded
    if (document.readyState === "complete") {
      setContentLoaded(true);
      checkAllLoaded();
    } else {
      window.addEventListener("load", handleLoad);
      return () => window.removeEventListener("load", handleLoad);
    }
  }, []);

  return (
    <LenisProvider>
      <>
        {/* Preloader */}
        <Preloader
          animationData={preloadedAnimation}
          onLoaded={handleAnimationLoaded}
          isLoading={isLoading}
        />

        <Navbar />
        <main>
          <Suspense>
            <Routes>
              {/* Visitor Routes */}
              <Route path="/" element={<Home />} />
              <Route path="/contact" element={<Contact />} />
              <Route path="/info" element={<Info />} />
              <Route path="/thank-you" element={<ThankYou />} />

              {/* Authentication - Prevent access for logged-in users */}
              <Route
                path="/auth"
                element={
                  <ProtectedRoute preventAuth={true}>
                    <Auth />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/signup"
                element={
                  <ProtectedRoute preventAuth={true}>
                    <Signup />
                  </ProtectedRoute>
                }
              />
              <Route path="/otp-verification" element={<OtpVerification />} />

              {/* Buyer Routes */}
              <Route path="/buyer/dashboard" element={<BuyerDashboard />} />
              <Route path="/buyer/details/:id" element={<ItemDetails />} />
              <Route path="/buyer/orders" element={<BuyerOrders />} />
              <Route path="/buyer/settings" element={<BuyerSettings />} />

              {/* Buyer Account Routes */}
              <Route
                path="/buyer/account/dashboard"
                element={<BuyerAccount />}
              />
              <Route path="/buyer/account/profile" element={<BuyerAccount />} />
              <Route
                path="/buyer/account/downloads"
                element={<BuyerAccount />}
              />
              <Route
                path="/buyer/account/requests"
                element={<BuyerAccount />}
              />
              <Route path="/buyer/account/bids" element={<BuyerAccount />} />
              <Route path="/buyer/account/cards" element={<BuyerAccount />} />

              {/* Seller Routes */}
              <Route path="/seller/dashboard" element={<SellerDashboard />} />
              <Route
                path="/seller/my-sports-strategies"
                element={<SellerMySportsStrategies />}
              />
              <Route
                path="/seller/my-sports-strategies/add"
                element={<AddNewStrategy />}
              />
              <Route path="/seller/requests" element={<SellerRequests />} />
              <Route path="/seller/bids" element={<SellerBids />} />
              <Route path="/seller/cards" element={<SellerCards />} />
              <Route path="/seller/profile" element={<SellerProfile />} />
              <Route path="/seller/my-content" element={<SellerMyContent />} />
              <Route path="/seller/settings" element={<SellerSettings />} />

              {/* Catch-all route */}
              <Route path="*" element={<Home />} />
            </Routes>
          </Suspense>
        </main>
        {/* Footer */}
        <Footer />
        {/* Scroll to Top Button */}
        <ScrollToTop />

        {/* Toast Container */}
        <ToastContainer
          position="top-right"
          autoClose={5000}
          hideProgressBar={false}
          newestOnTop={false}
          closeOnClick
          rtl={false}
          pauseOnFocusLoss
          draggable
          pauseOnHover
          theme="light"
        />
      </>
    </LenisProvider>
  );
};

export default App;
